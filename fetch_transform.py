import requests
import pandas as pd
import concurrent.futures
import time

# 读取ID文件
def read_ids(file_path='id.txt'):
    with open(file_path, 'r') as f:
        return [line.strip() for line in f if line.strip()]

# 调用API获取数据
def fetch_data(id):
    try:
        response = requests.get(f"http://127.0.0.1:8086/open/tranform?id={id}", timeout=10)
        data = response.json()
        return id, data
    except Exception as e:
        print(f"请求ID {id} 失败: {str(e)}")
        return id, {"code": -1, "msg": str(e), "data": None}

# 主函数
def main():
    # 读取所有ID
    ids = read_ids()
    results = []
    failed_ids = []
    
    # 使用线程池进行并发请求，最多6个并发
    with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
        future_to_id = {executor.submit(fetch_data, id): id for id in ids}
        for future in concurrent.futures.as_completed(future_to_id):
            id, data = future.result()
            if data["code"] == 200:
                results.append({"id": id, "result": data["data"]})
            else:
                failed_ids.append(id)
    
    # 将结果写入Excel
    if results:
        df = pd.DataFrame(results)
        df.to_excel("results.xlsx", index=False)
        print(f"成功处理 {len(results)} 个ID，结果已写入results.xlsx")
    
    # 将失败的ID写入fail.txt
    if failed_ids:
        with open("fail.txt", "w") as f:
            for id in failed_ids:
                f.write(f"{id}\n")
        print(f"有 {len(failed_ids)} 个ID处理失败，已写入fail.txt")

if __name__ == "__main__":
    main()