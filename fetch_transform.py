import requests
import pandas as pd
import concurrent.futures
import time
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 读取ID文件
def read_ids(file_path='id.txt'):
    with open(file_path, 'r') as f:
        return [line.strip() for line in f if line.strip()]

# 创建带重试机制的session
def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,  # 总重试次数
        backoff_factor=1,  # 重试间隔倍数
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 调用API获取数据（带重试机制）
def fetch_data(id, session=None):
    if session is None:
        session = create_session()

    max_retries = 3
    for attempt in range(max_retries):
        try:
            logger.info(f"正在请求ID {id}，第 {attempt + 1} 次尝试")
            response = session.get(
                f"http://127.0.0.1:8086/open/transform?id={id}",
                timeout=300  # 增加超时时间到300秒
            )
            response.raise_for_status()  # 检查HTTP状态码
            data = response.json()
            logger.info(f"ID {id} 请求成功")
            return id, data
        except requests.exceptions.Timeout:
            logger.warning(f"请求ID {id} 超时，第 {attempt + 1} 次尝试")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
                continue
        except requests.exceptions.ConnectionError as e:
            logger.warning(f"请求ID {id} 连接错误: {str(e)}，第 {attempt + 1} 次尝试")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue
        except Exception as e:
            logger.error(f"请求ID {id} 发生未知错误: {str(e)}")
            break

    logger.error(f"请求ID {id} 最终失败")
    return id, {"code": -1, "msg": "请求失败", "data": None}

# 主函数
def main():
    # 读取所有ID
    ids = read_ids()
    results = []
    failed_ids = []

    logger.info(f"开始处理 {len(ids)} 个ID")

    # 创建共享的session
    session = create_session()

    # 使用线程池进行并发请求，减少并发数到6个
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        # 提交所有任务
        future_to_id = {
            executor.submit(fetch_data, id, session): id
            for id in ids
        }

        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_id):
            try:
                id, data = future.result()
                if isinstance(data, dict) and data.get("code") == 200:
                    results.append({"id": id, "result": data["data"]})
                    logger.info(f"ID {id} 处理成功")
                else:
                    failed_ids.append(id)
                    logger.warning(f"ID {id} 处理失败: {data.get('msg', '未知错误')}")
            except Exception as e:
                # 从future_to_id中获取对应的id
                failed_id = future_to_id[future]
                failed_ids.append(failed_id)
                logger.error(f"ID {failed_id} 处理异常: {str(e)}")

            # 在每个请求之间添加小延迟，减轻服务器压力
            time.sleep(0.5)

    # 将结果写入Excel
    if results:
        df = pd.DataFrame(results)
        df.to_excel("results.xlsx", index=False)
        logger.info(f"成功处理 {len(results)} 个ID，结果已写入results.xlsx")
        print(f"✅ 成功处理 {len(results)} 个ID，结果已写入results.xlsx")

    # 将失败的ID写入fail.txt
    if failed_ids:
        with open("fail.txt", "w") as f:
            for id in failed_ids:
                f.write(f"{id}\n")
        logger.warning(f"有 {len(failed_ids)} 个ID处理失败，已写入fail.txt")
        print(f"⚠️  有 {len(failed_ids)} 个ID处理失败，已写入fail.txt")

    # 总结
    total = len(ids)
    success = len(results)
    failed = len(failed_ids)
    print(f"\n📊 处理总结:")
    print(f"   总计: {total} 个ID")
    print(f"   成功: {success} 个ID ({success/total*100:.1f}%)")
    print(f"   失败: {failed} 个ID ({failed/total*100:.1f}%)")

if __name__ == "__main__":
    main()